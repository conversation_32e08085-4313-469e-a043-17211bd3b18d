#!/usr/bin/env python3
"""
Script pour installer les dépendances nécessaires pour les optimisations MongoDB
"""

import subprocess
import sys
import os

def install_package(package):
    """Installe un package Python"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installé avec succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'installation de {package}: {e}")
        return False

def check_package(package):
    """Vérifie si un package est installé"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """Fonction principale"""
    print("🔧 Installation des dépendances pour les optimisations MongoDB")
    print("=" * 60)
    
    # Liste des dépendances requises
    dependencies = [
        ("pymongo", "pymongo>=4.0.0"),
        ("dotenv", "python-dotenv"),
        ("flask", "flask"),
        ("cloudinary", "cloudinary"),
        ("apscheduler", "APScheduler")
    ]
    
    missing_packages = []
    
    # Vérifier quels packages sont manquants
    print("🔍 Vérification des dépendances...")
    for import_name, package_name in dependencies:
        if check_package(import_name):
            print(f"✅ {package_name} déjà installé")
        else:
            print(f"❌ {package_name} manquant")
            missing_packages.append(package_name)
    
    if not missing_packages:
        print("\n🎉 Toutes les dépendances sont déjà installées!")
        return True
    
    # Installer les packages manquants
    print(f"\n📦 Installation de {len(missing_packages)} package(s) manquant(s)...")
    
    failed_installations = []
    for package in missing_packages:
        print(f"\n🔄 Installation de {package}...")
        if not install_package(package):
            failed_installations.append(package)
    
    # Résumé
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DE L'INSTALLATION")
    print("=" * 60)
    
    if not failed_installations:
        print("🎉 Toutes les dépendances ont été installées avec succès!")
        print("\n✅ Vous pouvez maintenant exécuter:")
        print("   python deploy_optimizations.py")
        return True
    else:
        print(f"⚠️ {len(failed_installations)} package(s) n'ont pas pu être installés:")
        for package in failed_installations:
            print(f"   ❌ {package}")
        
        print("\n💡 Solutions possibles:")
        print("1. Vérifiez votre connexion internet")
        print("2. Mettez à jour pip: python -m pip install --upgrade pip")
        print("3. Installez manuellement: pip install <package_name>")
        print("4. Utilisez un environnement virtuel")
        
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
