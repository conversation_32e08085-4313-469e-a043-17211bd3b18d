from flask import Flask, request, jsonify, Response
from apscheduler.schedulers.background import BackgroundScheduler
from urllib.parse import unquote
import json
import re
import calendar
from datetime import datetime, timedelta
import os

import base64
import gzip
import cloudinary
import cloudinary.uploader

from pymongo import MongoClient

# Charger les variables d'environnement du fichier .env
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Si python-dotenv n'est pas installé, continuer sans
    print("⚠️ python-dotenv non installé. Utilisez les variables d'environnement système.")
    pass


app = Flask(__name__)




# Récupérer les variables d'environnement
cloud_name = os.getenv("CLOUD_NAME")
api_key = os.getenv("API_KEY")
api_secret = os.getenv("API_SECRET")
mongo_uri = os.getenv("MONGODB_URI")

# Configuration Cloudinary
cloudinary.config(
    cloud_name=cloud_name,
    api_key=api_key,
    api_secret=api_secret
)

# Configuration optimisée de la connexion MongoDB avec pooling
client = MongoClient(
    mongo_uri,
    # Configuration du pool de connexions
    maxPoolSize=50,          # Maximum 50 connexions simultanées
    minPoolSize=5,           # Minimum 5 connexions maintenues
    maxIdleTimeMS=30000,     # Fermer les connexions inactives après 30s
    waitQueueTimeoutMS=5000, # Timeout de 5s pour obtenir une connexion

    # Configuration des timeouts
    serverSelectionTimeoutMS=5000,  # 5s pour sélectionner un serveur
    socketTimeoutMS=20000,          # 20s timeout pour les opérations socket
    connectTimeoutMS=10000,         # 10s timeout pour la connexion initiale

    # Configuration de la résilience
    retryWrites=True,               # Retry automatique des écritures
    retryReads=True,                # Retry automatique des lectures

    # Configuration de la compression
    compressors='snappy,zlib',      # Compression des données
)

db = client["Fasti"]
collection_Produit = db["Produit"]
collection_reference = db["reference"]
collection_cache = db["cache"]
collection_users = db["users"]
collection_statistics = db["statistics"]
collection_publications = db["publications"]
collection_pricing = db["pricing"]














#################################################################################

# Configuration
DOMAIN = "fastiapp.onrender.com"  # Votre domaine
APP_PACKAGE = "com.BenallaouaRayan.fastidz"  # Package de votre app Android

@app.route('/produit/<user_id>/<product_id>/<path:image>')
def produit_deeplink(user_id, product_id, image):
    # User agent (conservé pour usage futur)
    user_agent = request.headers.get('User-Agent', '').lower()
    _ = user_agent  # Éviter le warning

    # Données simulées (à remplacer plus tard par get_product_data)
    image_url = unquote(image)
    title = "Fasti 🌍"
    description = "Découvrez ce produit exclusif !"
    
    # Deep link
    deep_link_url = f"fastidz://produit/{user_id}/{product_id}"
    play_store_url = f"https://play.google.com/store/apps/details?id={APP_PACKAGE}"
    public_url = f"https://{DOMAIN}/produit/{user_id}/{product_id}"

    return f"""
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Redirection...</title>

        <!-- Balises Open Graph pour WhatsApp et autres -->
        <meta property="og:title" content="{title}">
        <meta property="og:description" content="{description}">
        <meta property="og:image" content="{image_url}">
        <meta property="og:url" content="{public_url}">
        <meta property="og:type" content="website">

        <!-- Script de redirection uniquement si ce n'est pas un bot (ex: WhatsApp) -->
        <script>
            const userAgent = navigator.userAgent.toLowerCase();
            const isBot = /whatsapp|facebook|twitter|meta|slack|discord/.test(userAgent);
            const isAndroid = /android/.test(userAgent);

            if (!isBot && isAndroid) {{
                window.onload = function() {{
                    setTimeout(function() {{
                        window.location.href = "{play_store_url}";
                    }}, 2000);
                    window.location.href = "{deep_link_url}";
                }};
            }}
        </script>
    </head>
    <body>
        <div style="text-align:center; margin-top:50px; font-family:sans-serif;">
            <h2>Ouverture de l'application...</h2>
            <p>Si rien ne se passe, vous serez redirigé vers le Play Store.</p>
        </div>
    </body>
    </html>
    """




# Fichier de validation Android App Links
@app.route('/.well-known/assetlinks.json')
def android_assetlinks():
    """
    Fichier de validation pour Android App Links
    """
    return jsonify([{
        "relation": ["delegate_permission/common.handle_all_urls"],
        "target": {
            "namespace": "android_app",
            "package_name": APP_PACKAGE,
            "sha256_cert_fingerprints": [
                "30:E5:A6:31:6D:C5:31:0E:47:61:FB:AA:E6:4C:C7:E4:DB:FC:50:D1:30:95:6B:B1:B0:3C:27:A8:3C:B2:5F:08"  # Remplacez par votre SHA256
            ]
        }
    }])




##################################################################################





@app.route("/utilisateur/<uid>", methods=["GET"])
def get_utilisateur_info(uid):
    try:
        utilisateur = collection_users.find_one({"id": uid})
        if utilisateur:
            # Extraction des champs
            utilisateur_data = utilisateur.get("utilisateur", {})
            nom = utilisateur_data.get("nom")
            image_profil = utilisateur_data.get("image_profil")
            image_couverture = utilisateur_data.get("image_couverture")
            description = utilisateur_data.get("description")
            membre = utilisateur.get("membre")

            # Création de la liste
            resultat = [nom, image_profil, image_couverture, membre , description ,datetime.now().isoformat()]

            return jsonify(resultat), 200
        else:
            return jsonify({"message": "Utilisateur non trouvé"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400











# Plus besoin de dossier local - tout est sur Cloudinary
# UPLOAD_FOLDER = "users"
# os.makedirs(UPLOAD_FOLDER, exist_ok=True)


def start_scheduler():
    scheduler = BackgroundScheduler()
    scheduler.add_job(tache_quotidienne, 'cron', hour=0, minute=1)
    scheduler.start()








def compresser(texte):
    """
    Compresse une chaÃ®ne de caractÃ¨res avec gzip et encode en base64
    pour la transmission/stockage
    """
    # Convertir en bytes
    text_bytes = texte.encode('utf-8')
    
    # Compresser avec gzip (niveau 9 = compression maximale)
    compressed = gzip.compress(text_bytes, compresslevel=9)
    
    # Encoder en base64 pour faciliter la transmission
    encoded = base64.b64encode(compressed).decode('ascii')
    
    return encoded


def decompresser(data):
    compressed_bytes = base64.b64decode(data.encode('ascii'))
        
       
    decompressed = gzip.decompress(compressed_bytes)
        
        # Convertir en string
    return decompressed.decode('utf-8')







# Fonctions utilitaires pour la compression automatique des réponses et requêtes

def jsonify_compressed(data):
    """
    Fonction pour retourner une réponse JSON compressée
    """
    try:
        # Convertir les données en JSON
        json_string = json.dumps(data, ensure_ascii=False, separators=(',', ':'))

        # Compresser les données
        compressed_data = compresser(json_string)

        # Retourner une réponse avec le header indiquant la compression
        response = Response(
            compressed_data,
            content_type='application/json',
            headers={'X-Compressed': 'true'}
        )


        return response

    except Exception as e:
        #print(f"❌ Erreur compression: {e}")
        # En cas d'erreur, retourner la réponse normale
        return jsonify(data)
    
    
    

def get_request_data():
    """
    Fonction pour récupérer et décompresser automatiquement les données de requête
    """
    try:
        # Vérifier si la requête contient des données compressées
        if request.headers.get('X-Compressed') == 'true':
            # Récupérer les données compressées du body
            compressed_data = request.get_data(as_text=True)

            if not compressed_data:
                #print("⚠️ Header X-Compressed présent mais pas de données")
                return request.get_json()

            # Décompresser les données
            json_string = decompresser(compressed_data)

            # Parser le JSON
            data = json.loads(json_string)


            return data
        else:
            # Données non compressées, utiliser la méthode normale
            return request.get_json()

    except Exception as e:
        #print(f"❌ Erreur décompression: {e}")
        #print(f"Headers: {dict(request.headers)}")
        #print(f"Data: {request.get_data(as_text=True)[:200]}...")
        # En cas d'erreur, essayer la méthode normale
        try:
            return request.get_json()
        except:
            return None









def ajouter_valeur_dans_graphe_mongodb(uid, cle, valeur):
    """
    Version MongoDB ultra-optimisée avec mise en cache et opérations atomiques
    """
    today = datetime.today()
    current_year = today.year
    current_month = today.month
    current_day = today.day
    today_str = today.strftime('%Y-%m-%d')
    current_month_str = f"{current_year}-{current_month:02d}"

    try:
        # Utiliser une opération atomique avec upsert pour éviter les conditions de course
        result = collection_statistics.find_one_and_update(
            {"uid": uid},
            {
                "$setOnInsert": {
                    "uid": uid,
                    "statistiques": {"graphes": {}},
                    "_historique_dates": {}
                }
            },
            upsert=True,
            return_document=True  # Retourne le document après mise à jour
        )

        if not result:
            # Si le document n'existe toujours pas, le créer explicitement
            stats_doc = {
                "uid": uid,
                "statistiques": {"graphes": {}},
                "_historique_dates": {}
            }
            collection_statistics.insert_one(stats_doc)
            result = stats_doc

        graphes = result.get("statistiques", {}).get("graphes", {})
        historique = result.get("_historique_dates", {})

        # Vérifier si la clé existe dans les graphes
        if cle not in graphes:
            # La clé n'existe pas, initialiser avec des zéros
            graphes[cle] = []

            # Pour chaque mois de janvier jusqu'au mois actuel
            for month in range(1, current_month + 1):
                num_days = calendar.monthrange(current_year, month)[1]

                if month == current_month:
                    month_data = [0] * (current_day - 1)  # Zéros jusqu'à hier
                    month_data.append(valeur)  # Valeur pour aujourd'hui
                else:
                    month_data = [0] * num_days

                graphes[cle].append(month_data)

            # Mettre à jour l'historique des dates
            historique[cle] = {
                "mois": current_month_str,
                "jour": today_str
            }
        else:
            # La clé existe déjà, vérifier si nous sommes dans le même mois/jour
            if cle in historique:
                hist_entry = historique[cle]
                last_month_str = hist_entry["mois"]
                last_day_str = hist_entry["jour"]

                last_year, last_month = map(int, last_month_str.split('-'))
                last_date = datetime.strptime(last_day_str, '%Y-%m-%d')

                if last_month_str == current_month_str:
                    # Même mois
                    if last_day_str == today_str:
                        # Même jour, additionner à la dernière valeur
                        graphes[cle][-1][-1] += valeur
                    else:
                        # Différent jour, mais même mois
                        days_diff = (today - last_date).days

                        # S'il y a des jours manquants
                        if days_diff > 1:
                            # Ajouter des zéros pour les jours manquants
                            for _ in range(days_diff - 1):
                                graphes[cle][-1].append(0)

                        # Ajouter la valeur pour aujourd'hui
                        graphes[cle][-1].append(valeur)
                else:
                    # Différent mois - logique de mise à jour complexe
                    last_month_days = calendar.monthrange(last_year, last_month)[1]
                    days_to_add = last_month_days - len(graphes[cle][-1])

                    if days_to_add > 0:
                        graphes[cle][-1].extend([0] * days_to_add)

                    # Ajouter des listes de zéros pour tous les mois manquants
                    next_month = last_month + 1
                    next_year = last_year

                    if next_month > 12:
                        next_month = 1
                        next_year += 1

                    while (next_year < current_year) or (next_year == current_year and next_month < current_month):
                        days_in_month = calendar.monthrange(next_year, next_month)[1]
                        graphes[cle].append([0] * days_in_month)

                        next_month += 1
                        if next_month > 12:
                            next_month = 1
                            next_year += 1

                    # Ajouter le mois courant avec des zéros jusqu'à hier et la valeur pour aujourd'hui
                    month_data = [0] * (current_day - 1)
                    month_data.append(valeur)
                    graphes[cle].append(month_data)

                # Mettre à jour l'historique
                historique[cle] = {
                    "mois": current_month_str,
                    "jour": today_str
                }
            else:
                # Cas improbable: la clé existe dans les graphes mais pas dans l'historique
                # Nous réinitialisons tout
                graphes[cle] = []

                # Pour chaque mois de janvier jusqu'au mois actuel
                for month in range(1, current_month + 1):
                    num_days = calendar.monthrange(current_year, month)[1]

                    if month == current_month:
                        month_data = [0] * (current_day - 1)
                        month_data.append(valeur)
                    else:
                        month_data = [0] * num_days

                    graphes[cle].append(month_data)

                historique[cle] = {
                    "mois": current_month_str,
                    "jour": today_str
                }

        # Utiliser une opération atomique pour la mise à jour finale
        update_result = collection_statistics.update_one(
            {"uid": uid},
            {
                "$set": {
                    f"statistiques.graphes.{cle}": graphes[cle],
                    f"_historique_dates.{cle}": historique[cle]
                }
            }
        )

        return update_result.modified_count > 0 or update_result.upserted_id is not None

    except Exception as e:
        #print(f"❌ Erreur dans ajouter_valeur_dans_graphe_mongodb: {e}")
        return False


def ajouter_valeurs_multiples_dans_graphe_mongodb(uid, valeurs_dict):
    """
    Version optimisée pour ajouter plusieurs valeurs en une seule opération
    valeurs_dict: {"cle1": valeur1, "cle2": valeur2, ...}
    """
    if not valeurs_dict:
        return True

    today = datetime.today()
    current_year = today.year
    current_month = today.month
    current_day = today.day
    today_str = today.strftime('%Y-%m-%d')
    current_month_str = f"{current_year}-{current_month:02d}"

    try:
        # Récupérer le document une seule fois
        stats_doc = collection_statistics.find_one({"uid": uid})

        if not stats_doc:
            stats_doc = {
                "uid": uid,
                "statistiques": {"graphes": {}},
                "_historique_dates": {}
            }

        graphes = stats_doc.get("statistiques", {}).get("graphes", {})
        historique = stats_doc.get("_historique_dates", {})

        # Traiter toutes les clés en une seule fois
        updates = {}

        for cle, valeur in valeurs_dict.items():
            # Logique similaire à la fonction originale mais pour toutes les clés
            if cle not in graphes:
                graphes[cle] = []
                for month in range(1, current_month + 1):
                    num_days = calendar.monthrange(current_year, month)[1]
                    if month == current_month:
                        month_data = [0] * (current_day - 1)
                        month_data.append(valeur)
                    else:
                        month_data = [0] * num_days
                    graphes[cle].append(month_data)

                historique[cle] = {
                    "mois": current_month_str,
                    "jour": today_str
                }
            else:
                # Logique de mise à jour existante...
                if cle in historique:
                    hist_entry = historique[cle]
                    last_day_str = hist_entry["jour"]

                    if last_day_str == today_str:
                        graphes[cle][-1][-1] += valeur
                    else:
                        # Ajouter la valeur pour aujourd'hui
                        graphes[cle][-1].append(valeur)
                        historique[cle]["jour"] = today_str

            # Préparer les mises à jour
            updates[f"statistiques.graphes.{cle}"] = graphes[cle]
            updates[f"_historique_dates.{cle}"] = historique[cle]

        # Une seule opération de mise à jour pour toutes les clés
        if updates:
            collection_statistics.update_one(
                {"uid": uid},
                {"$set": updates},
                upsert=True
            )

        return True

    except Exception as e:
        #print(f"❌ Erreur dans ajouter_valeurs_multiples_dans_graphe_mongodb: {e}")
        return False

def ajouter_valeur_dans_graphe_prix_mongodb(cle, valeur):
    """
    Version MongoDB optimisée pour les statistiques de prix globales
    """
    try:
        today = datetime.today()
        current_year = today.year
        current_month = today.month
        current_day = today.day

        # Chercher le document de pricing
        pricing_doc = collection_pricing.find_one({"type": "global"})

        if not pricing_doc:
            # Créer un nouveau document de pricing
            pricing_doc = {
                "type": "global",
                "statistiques": {"graphes": {}},
                "_historique_dates": {}
            }

        graphes = pricing_doc.get("statistiques", {}).get("graphes", {})
        historique = pricing_doc.get("_historique_dates", {})

        # Même logique que la fonction précédente mais pour les prix globaux
        if cle not in graphes:
            graphes[cle] = []

            for month in range(1, current_month + 1):
                num_days = calendar.monthrange(current_year, month)[1]

                if month == current_month:
                    month_data = [0] * (current_day - 1)
                    month_data.append(valeur)
                else:
                    month_data = [0] * num_days

                graphes[cle].append(month_data)

            historique[cle] = {
                "mois": f"{current_year}-{current_month:02d}",
                "jour": today.strftime('%Y-%m-%d')
            }
        else:
            # Logique de mise à jour similaire à la fonction utilisateur
            if cle in historique:
                hist_entry = historique[cle]
                last_month_str = hist_entry["mois"]
                last_day_str = hist_entry["jour"]

                last_year, last_month = map(int, last_month_str.split('-'))
                last_date = datetime.strptime(last_day_str, '%Y-%m-%d')

                current_month_str = f"{current_year}-{current_month:02d}"

                if last_month_str == current_month_str:
                    if last_day_str == today.strftime('%Y-%m-%d'):
                        graphes[cle][-1][-1] += valeur
                    else:
                        days_diff = (today - last_date).days
                        if days_diff > 1:
                            for _ in range(days_diff - 1):
                                graphes[cle][-1].append(0)
                        graphes[cle][-1].append(valeur)
                else:
                    # Logique pour différent mois (similaire à la fonction utilisateur)
                    last_month_days = calendar.monthrange(last_year, last_month)[1]
                    days_to_add = last_month_days - len(graphes[cle][-1])

                    if days_to_add > 0:
                        graphes[cle][-1].extend([0] * days_to_add)

                    next_month = last_month + 1
                    next_year = last_year

                    if next_month > 12:
                        next_month = 1
                        next_year += 1

                    while (next_year < current_year) or (next_year == current_year and next_month < current_month):
                        days_in_month = calendar.monthrange(next_year, next_month)[1]
                        graphes[cle].append([0] * days_in_month)

                        next_month += 1
                        if next_month > 12:
                            next_month = 1
                            next_year += 1

                    month_data = [0] * (current_day - 1)
                    month_data.append(valeur)
                    graphes[cle].append(month_data)

                historique[cle] = {
                    "mois": current_month_str,
                    "jour": today.strftime('%Y-%m-%d')
                }

        # Sauvegarder dans MongoDB
        pricing_doc["statistiques"]["graphes"] = graphes
        pricing_doc["_historique_dates"] = historique

        collection_pricing.update_one(
            {"type": "global"},
            {"$set": pricing_doc},
            upsert=True
        )

        return True

    except Exception as e:
        #print(f"❌ Erreur dans ajouter_valeur_dans_graphe_prix_mongodb: {e}")
        return False













def tache_quotidienne():
    # Migration vers MongoDB - plus besoin de fichier prix.json
    repertoire = "users"
    resultat = calculer_ratio(repertoire)

    # Utiliser la nouvelle fonction MongoDB pour les prix
    ajouter_valeur_dans_graphe_prix_mongodb("prix", float(str(resultat[1]) + str(resultat[0])))



    nettoyer_pub_expiree()
    
    
    


def calculer_ratio(repertoire_parent=None):
    """
    Version MongoDB ultra-optimisée avec requêtes groupées et opérations en lot
    """
    _ = repertoire_parent  # Paramètre conservé pour compatibilité
    try:
        # Utiliser une agrégation pour calculer A et B en une seule requête
        pipeline = [
            {
                "$group": {
                    "_id": None,
                    "count": {"$sum": 1},  # Compter les utilisateurs (A)
                    "total_solde": {
                        "$sum": {
                            "$toDouble": {
                                "$ifNull": ["$utilisateur.solde", 0]
                            }
                        }
                    }  # Somme des soldes (B)
                }
            }
        ]

        result = list(collection_users.aggregate(pipeline))

        if not result:
            return "Erreur : aucun utilisateur trouvé"

        A = result[0]["count"]
        B = result[0]["total_solde"]

        # Récupérer tous les IDs utilisateurs pour les statistiques
        user_ids = list(collection_users.find({}, {"id": 1}))

        # Utiliser la fonction optimisée pour ajouter les valeurs en lot
        for user_doc in user_ids:
            uid = user_doc.get("id")
            if uid:
                # Utiliser la nouvelle fonction pour ajouter plusieurs valeurs en une fois
                valeurs_stats = {
                    "vues": 0,
                    "interet_publicite": 0,
                    "clics_publicite": 0,
                    "Profile": 0
                }
                ajouter_valeurs_multiples_dans_graphe_mongodb(uid, valeurs_stats)

        if B == 0:
            return "Erreur : division par zéro (somme des soldes = 0)"

        return A, f"{B:.2f}"

    except Exception as e:
        #print(f"❌ Erreur dans calculer_ratio: {e}")
        # Fallback vers l'ancienne méthode
        return calculer_ratio_fallback()


def calculer_ratio_fallback():
    """
    Méthode de fallback en cas d'erreur avec la version optimisée
    """
    A = 0
    B = 0

    users = list(collection_users.find({}, {"id": 1, "utilisateur.solde": 1}))

    for user_doc in users:
        uid = user_doc.get("id")
        if uid:
            A += 1
            try:
                solde = float(user_doc.get("utilisateur", {}).get("solde", 0))
                B += solde
            except:
                pass

    if B == 0:
        return "Erreur : division par zéro (somme des soldes = 0)"

    return A, f"{B:.2f}"





    





###############################################




def nettoyer_pub_expiree():
    """
    Version MongoDB ultra-optimisée avec opérations en lot et requêtes groupées
    """
    aujourd_hui = datetime.now().strftime('%Y-%m-%d')
    time_expertion = datetime.now().strftime('%d/%m/%Y/%H/%M/%S')

    try:
        # Récupérer toutes les publications expirées en une seule requête
        publications_expirees = list(collection_publications.find({
            "pub": {"$lte": aujourd_hui}
        }))

        if not publications_expirees:
            return  # Rien à nettoyer

        # Organiser les publications par pays/document pour optimiser les mises à jour
        updates_by_doc = {}
        user_updates = {}
        medias_to_delete = []

        # Première passe : organiser les données
        for entree in publications_expirees:
            uid = entree["uuid"]
            produit_id = entree["produit"]
            pays = entree["pays"]
            categorie = entree["categorie"]
            doc_id = int(pays)

            # Organiser par document
            if doc_id not in updates_by_doc:
                updates_by_doc[doc_id] = {}
            if categorie not in updates_by_doc[doc_id]:
                updates_by_doc[doc_id][categorie] = []

            updates_by_doc[doc_id][categorie].append({
                "uuid": uid,
                "produit": produit_id,
                "time_expertion": time_expertion
            })

            # Organiser les mises à jour utilisateur
            if uid not in user_updates:
                user_updates[uid] = []
            user_updates[uid].append(produit_id)

        # Deuxième passe : traiter les documents Produit en lot
        produit_bulk_operations = []
        modified_files = []

        for doc_id, categories in updates_by_doc.items():
            produit_doc = collection_Produit.find_one({"_id": doc_id})
            if not produit_doc:
                continue

            doc_modified = False

            for categorie, updates in categories.items():
                if categorie not in produit_doc:
                    continue

                # Mettre à jour tous les produits de cette catégorie
                for obj in produit_doc[categorie]:
                    for update_info in updates:
                        if (obj.get("uuid") == update_info["uuid"] and
                            obj.get("produit") == update_info["produit"]):
                            obj["pub"] = ""
                            obj["Time"] = update_info["time_expertion"]
                            doc_modified = True

            if doc_modified:
                # Préparer l'opération de mise à jour en lot
                produit_bulk_operations.append({
                    "updateOne": {
                        "filter": {"_id": doc_id},
                        "update": {"$set": {k: v for k, v in produit_doc.items() if k != "_id"}}
                    }
                })
                modified_files.append(f"{doc_id}.json")

        # Exécuter toutes les mises à jour de produits en lot
        if produit_bulk_operations:
            collection_Produit.bulk_write(produit_bulk_operations, ordered=False)

        # Troisième passe : traiter les utilisateurs en lot
        user_bulk_operations = []

        # Récupérer tous les utilisateurs concernés en une seule requête
        user_ids = list(user_updates.keys())
        if user_ids:
            users_docs = list(collection_users.find(
                {"id": {"$in": user_ids}},
                {"id": 1, "produits": 1}
            ))

            for user_doc in users_docs:
                uid = user_doc["id"]
                produits_to_update = user_updates.get(uid, [])

                if "produits" in user_doc:
                    produits = user_doc["produits"]
                    user_modified = False

                    for produit in produits:
                        if produit and produit[0] in produits_to_update:
                            # Supprimer le dernier élément de la liste "images"
                            if len(produit) > 12 and isinstance(produit[12], dict):
                                images = produit[12].get("images", [])
                                if images:
                                    dernier = images.pop()
                                    medias_to_delete.append(dernier)

                                if len(produit) > 13:
                                    produit[13] = ""
                                if len(produit) > 14 and isinstance(produit[14], dict):
                                    produit[14]["Time"] = time_expertion

                                user_modified = True

                    if user_modified:
                        user_bulk_operations.append({
                            "updateOne": {
                                "filter": {"id": uid},
                                "update": {"$set": {"produits": produits}}
                            }
                        })

        # Exécuter toutes les mises à jour utilisateur en lot
        if user_bulk_operations:
            collection_users.bulk_write(user_bulk_operations, ordered=False)

        # Supprimer les médias Cloudinary en arrière-plan
        for media_url in medias_to_delete:
            try:
                supprimer_media_cloudinary(media_url)
            except:
                pass  # Continuer même si la suppression échoue

        # Supprimer toutes les publications expirées en une seule opération
        collection_publications.delete_many({
            "pub": {"$lte": aujourd_hui}
        })

        # Mettre à jour les caches pour tous les fichiers modifiés
        for fichier in modified_files:
            update_all_caches(fichier)

        #print(f"✅ Nettoyage terminé: {len(publications_expirees)} publications expirées traitées")

    except Exception as e:
        #print(f"❌ Erreur lors du nettoyage des publications expirées: {e}")
        # Fallback vers l'ancienne méthode
        nettoyer_pub_expiree_fallback()


def nettoyer_pub_expiree_fallback():
    """
    Méthode de fallback pour le nettoyage des publications expirées
    """
    aujourd_hui = datetime.now().strftime('%Y-%m-%d')
    time_expertion = datetime.now().strftime('%d/%m/%Y/%H/%M/%S')

    publications_expirees = list(collection_publications.find({
        "pub": {"$lte": aujourd_hui}
    }))

    for entree in publications_expirees:
        try:
            uid = entree["uuid"]
            produit_id = entree["produit"]
            pays = entree["pays"]
            categorie = entree["categorie"]

            # Traitement individuel en cas d'erreur avec la version optimisée
            doc_id = int(pays)
            produit_doc = collection_Produit.find_one({"_id": doc_id})

            if produit_doc and categorie in produit_doc:
                for obj in produit_doc[categorie]:
                    if obj.get("uuid") == uid and obj.get("produit") == produit_id:
                        obj["pub"] = ""
                        obj["Time"] = time_expertion
                        break

                collection_Produit.update_one(
                    {"_id": doc_id},
                    {"$set": {categorie: produit_doc[categorie]}}
                )

        except Exception as e:
            #print(f"❌ Erreur pour la publication {entree}: {e}")
            continue

    # Supprimer les publications expirées
    collection_publications.delete_many({
        "pub": {"$lte": aujourd_hui}
    })
    
    


        
        
    
        
    
 

def update_user_device_cache(uid, device_id, file_id):
    """
    Version optimisée du cache pour envoyer seulement les différences nécessaires
    """
    # Récupérer les données Produit
    produit_doc = collection_Produit.find_one({"_id": file_id})
    if not produit_doc:
        return

    # Récupérer la référence pour ce device
    reference_doc = collection_reference.find_one({"uid": uid, "device_id": device_id, "file_id": file_id})
    reference_data = reference_doc.get("data", {}) if reference_doc else {}

    diff_data = {}
    all_categories = set(produit_doc.keys()).union(reference_data.keys()) - {"_id"}

    for cat in all_categories:
        produits_actuels = produit_doc.get(cat, [])
        produits_reference = reference_data.get(cat, [])

        # Optimisation: créer des index pour accès rapide
        actuels_index = {f"{p.get('uuid', '')}_{p.get('produit', '')}": p for p in produits_actuels}
        reference_index = {f"{p.get('uuid', '')}_{p.get('produit', '')}": p for p in produits_reference}

        # Ajouts (produits présents dans actuels mais pas dans référence)
        add = [p for id_key, p in actuels_index.items() if id_key not in reference_index]

        # Suppressions (produits présents dans référence mais pas dans actuels)
        delet = [{"uuid": p.get("uuid", ""), "produit": p.get("produit", "")}
                 for id_key, p in reference_index.items() if id_key not in actuels_index]

        # Mises à jour (produits présents dans les deux mais avec des différences)
        update = []
        for id_key in actuels_index.keys() & reference_index.keys():
            current = actuels_index[id_key]
            ref = reference_index[id_key]

            # Optimisation: comparer seulement les champs importants
            important_fields = ["image", 
                                "video", 
                                "nome", 
                                "prix",
                                "Promotion", 
                                "note",
                                "commentair", 
                                "view", 
                                "pub", 
                                "Time",
                                "utilisateur_info",
                                "uuid",
                                "produit"
                                ]
            changes = {}

            for field in important_fields:
                if field in current and current.get(field) != ref.get(field):
                    changes[field] = current[field]

            if changes:
                update.append({
                    "uuid": current.get("uuid", ""),
                    "produit": current.get("produit", ""),
                    **changes
                })

        # Seulement ajouter la catégorie si il y a des changements
        if add or delet or update:
            diff_data[cat] = {
                "add": add,
                "delet": delet,
                "update": update
            }

    # Mettre à jour ou insérer le cache dans MongoDB avec optimisation
    if diff_data:
        collection_cache.update_one(
            {"uid": uid, "device_id": device_id, "file_id": file_id},
            {"$set": diff_data},
            upsert=True
        )

    return diff_data



def update_user_cache(uid, file_id):
    """
    Version optimisée du cache utilisateur pour envoyer seulement les différences nécessaires
    """
    produit_doc = collection_Produit.find_one({"_id": file_id})
    if not produit_doc:
        return

    reference_doc = collection_reference.find_one({"uid": uid, "file_id": file_id, "device_id": None})
    reference_data = reference_doc.get("data", {}) if reference_doc else {}

    diff_data = {}
    all_categories = set(produit_doc.keys()).union(reference_data.keys()) - {"_id"}

    for cat in all_categories:
        produits_actuels = produit_doc.get(cat, [])
        produits_reference = reference_data.get(cat, [])

        # Optimisation: utiliser des index plus efficaces
        actuels_index = {f"{p.get('uuid', '')}_{p.get('produit', '')}": p for p in produits_actuels}
        reference_index = {f"{p.get('uuid', '')}_{p.get('produit', '')}": p for p in produits_reference}

        add = [p for id_key, p in actuels_index.items() if id_key not in reference_index]

        delet = [{"uuid": p.get("uuid", ""), "produit": p.get("produit", "")}
                 for id_key, p in reference_index.items() if id_key not in actuels_index]

        update = []
        for id_key in actuels_index.keys() & reference_index.keys():
            current = actuels_index[id_key]
            ref = reference_index[id_key]

            # Optimisation: comparer seulement les champs importants
            important_fields = ["image", "video", "nome", "prix", "Promotion", "note", "commentair", "view", "pub", "Time"]
            changes = {}

            for field in important_fields:
                if field in current and current.get(field) != ref.get(field):
                    changes[field] = current[field]

            if changes:
                update.append({
                    "uuid": current.get("uuid", ""),
                    "produit": current.get("produit", ""),
                    **changes
                })

        # Seulement ajouter la catégorie si il y a des changements
        if add or delet or update:
            diff_data[cat] = {
                "add": add,
                "delet": delet,
                "update": update
            }

    # Mettre à jour seulement si il y a des différences
    if diff_data:
        collection_cache.update_one(
            {"uid": uid, "file_id": file_id, "device_id": None},
            {"$set": diff_data},
            upsert=True
        )

    return diff_data




def update_all_caches(file_id):
    """
    Version ultra-optimisée pour mettre à jour tous les caches avec requêtes groupées
    """
    try:
        # Récupérer le document produit une seule fois
        produit_doc = collection_Produit.find_one({"_id": file_id})
        if not produit_doc:
            return

        # Récupérer tous les utilisateurs et leurs références en une seule requête optimisée
        # Utiliser une projection pour ne récupérer que les champs nécessaires
        users = list(collection_users.find({}, {"id": 1}))

        if not users:
            return

        # Récupérer toutes les références en une seule requête groupée
        user_ids = [user["id"] for user in users]
        all_references = list(collection_reference.find({
            "uid": {"$in": user_ids},
            "file_id": file_id
        }, {"uid": 1, "device_id": 1, "data": 1}))

        # Organiser les références par utilisateur pour un accès rapide
        references_by_user = {}
        for ref in all_references:
            uid = ref["uid"]
            if uid not in references_by_user:
                references_by_user[uid] = {"main": None, "devices": []}

            if ref.get("device_id") is None:
                references_by_user[uid]["main"] = ref
            else:
                references_by_user[uid]["devices"].append(ref)

        # Préparer les opérations de mise à jour en lot
        cache_updates = []
        reference_updates = []

        # Traitement optimisé par utilisateur
        for user in users:
            uid = user["id"]
            user_refs = references_by_user.get(uid, {"main": None, "devices": []})

            # Mise à jour du cache utilisateur principal
            main_ref = user_refs["main"]
            main_diff = calculate_cache_diff(produit_doc, main_ref.get("data", {}) if main_ref else {}, file_id)

            if main_diff:
                cache_updates.append({
                    "filter": {"uid": uid, "file_id": file_id, "device_id": None},
                    "update": {"$set": main_diff},
                    "upsert": True
                })

            # Mise à jour des caches des devices
            for device_ref in user_refs["devices"]:
                device_id = device_ref["device_id"]
                device_diff = calculate_cache_diff(produit_doc, device_ref.get("data", {}), file_id)

                if device_diff:
                    cache_updates.append({
                        "filter": {"uid": uid, "device_id": device_id, "file_id": file_id},
                        "update": {"$set": device_diff},
                        "upsert": True
                    })

            # Préparer la mise à jour des références
            reference_updates.append({
                "filter": {"uid": uid, "device_id": None, "file_id": file_id},
                "update": {"$set": {"data": produit_doc}},
                "upsert": True
            })

            for device_ref in user_refs["devices"]:
                reference_updates.append({
                    "filter": {"uid": uid, "device_id": device_ref["device_id"], "file_id": file_id},
                    "update": {"$set": {"data": produit_doc}},
                    "upsert": True
                })

        # Exécuter toutes les mises à jour en lot
        if cache_updates:
            # Utiliser bulk_write pour optimiser les performances
            cache_operations = [
                {"updateOne": {
                    "filter": op["filter"],
                    "update": op["update"],
                    "upsert": op["upsert"]
                }} for op in cache_updates
            ]
            if cache_operations:
                collection_cache.bulk_write(cache_operations, ordered=False)

        if reference_updates:
            reference_operations = [
                {"updateOne": {
                    "filter": op["filter"],
                    "update": op["update"],
                    "upsert": op["upsert"]
                }} for op in reference_updates
            ]
            if reference_operations:
                collection_reference.bulk_write(reference_operations, ordered=False)

        #print(f"✅ Caches mis à jour en lot pour {len(users)} utilisateurs sur le fichier {file_id}")

    except Exception as e:
        #print(f"❌ Erreur lors de la mise à jour des caches: {e}")
        # Fallback vers l'ancienne méthode en cas d'erreur
        update_all_caches_fallback(file_id)


def calculate_cache_diff(produit_doc, reference_data, file_id):
    """
    Calcule les différences pour le cache de manière optimisée
    """
    _ = file_id  # Paramètre conservé pour compatibilité mais non utilisé
    diff_data = {}
    all_categories = set(produit_doc.keys()).union(reference_data.keys()) - {"_id"}

    for cat in all_categories:
        produits_actuels = produit_doc.get(cat, [])
        produits_reference = reference_data.get(cat, [])

        # Optimisation: créer des index pour accès rapide
        actuels_index = {f"{p.get('uuid', '')}_{p.get('produit', '')}": p for p in produits_actuels}
        reference_index = {f"{p.get('uuid', '')}_{p.get('produit', '')}": p for p in produits_reference}

        # Ajouts
        add = [p for id_key, p in actuels_index.items() if id_key not in reference_index]

        # Suppressions
        delet = [{"uuid": p.get("uuid", ""), "produit": p.get("produit", "")}
                 for id_key, p in reference_index.items() if id_key not in actuels_index]

        # Mises à jour
        update = []
        important_fields = ["image", "video", "nome", "prix", "Promotion", "note",
                           "commentair", "view", "pub", "Time", "utilisateur_info", "uuid", "produit"]

        for id_key in actuels_index.keys() & reference_index.keys():
            current = actuels_index[id_key]
            ref = reference_index[id_key]
            changes = {}

            for field in important_fields:
                if field in current and current.get(field) != ref.get(field):
                    changes[field] = current[field]

            if changes:
                update.append({
                    "uuid": current.get("uuid", ""),
                    "produit": current.get("produit", ""),
                    **changes
                })

        # Seulement ajouter la catégorie si il y a des changements
        if add or delet or update:
            diff_data[cat] = {
                "add": add,
                "delet": delet,
                "update": update
            }

    return diff_data


def update_all_caches_fallback(file_id):
    """
    Méthode de fallback en cas d'erreur avec la version optimisée
    """
    users = list(collection_users.find({}, {"id": 1}))

    for user in users:
        uid = user["id"]
        try:
            update_user_cache(uid, file_id)

            device_refs = list(collection_reference.find({
                "uid": uid,
                "file_id": file_id,
                "device_id": {"$ne": None}
            }, {"device_id": 1}))

            for ref in device_refs:
                device_id = ref["device_id"]
                update_user_device_cache(uid, device_id, file_id)
        except Exception as e:
            #print(f"❌ Erreur pour l'utilisateur {uid}: {e}")
            continue


def update_reference(uid, device_id, file_id):
    """
    Version optimisée pour mettre à jour les références
    """
    produit_doc = collection_Produit.find_one({"_id": file_id})
    if not produit_doc:
        return

    ref_filter = {"uid": uid, "device_id": device_id, "file_id": file_id}

    collection_reference.update_one(
        ref_filter,
        {"$set": {"data": produit_doc}},
        upsert=True
    )

def supprimer_produits_par_uuid_mongodb(uid):
    """
    Version MongoDB optimisée pour supprimer tous les produits d'un utilisateur
    Inclut maintenant la suppression des médias Cloudinary
    """
    try:
        # D'abord, récupérer les données utilisateur pour supprimer les médias Cloudinary
        user_doc = collection_users.find_one({"id": uid})
        if user_doc and "produits" in user_doc:
            for produit in user_doc["produits"]:
                if len(produit) > 12:
                    # Supprimer l'image principale (index 9)
                    if len(produit) > 9 and produit[9]:
                        supprimer_media_cloudinary(produit[9])

                    # Supprimer la vidéo (index 10)
                    if len(produit) > 10 and produit[10]:
                        supprimer_media_cloudinary(produit[10])

                    # Supprimer les images supplémentaires (index 12)
                    if isinstance(produit[12], dict) and "images" in produit[12]:
                        for image_url in produit[12]["images"]:
                            if image_url:
                                supprimer_media_cloudinary(image_url)

        # Supprimer les images de profil et couverture Cloudinary
        if user_doc and "utilisateur" in user_doc:
            utilisateur = user_doc["utilisateur"]
            if "image_profil" in utilisateur and utilisateur["image_profil"]:
                supprimer_media_cloudinary(utilisateur["image_profil"])
            if "image_couverture" in utilisateur and utilisateur["image_couverture"]:
                supprimer_media_cloudinary(utilisateur["image_couverture"])

        # Récupérer tous les documents de produits
        produit_docs = collection_Produit.find({})

        for produit_doc in produit_docs:
            doc_id = produit_doc["_id"]
            modified = False

            # Parcourir toutes les catégories
            for categorie in produit_doc:
                if categorie == "_id":
                    continue

                produits_list = produit_doc[categorie]
                if isinstance(produits_list, list):
                    # Filtrer les produits qui n'appartiennent pas à cet utilisateur
                    new_list = [p for p in produits_list if p.get("uuid") != uid]

                    if len(new_list) != len(produits_list):
                        produit_doc[categorie] = new_list
                        modified = True

            # Sauvegarder si modifié
            if modified:
                collection_Produit.update_one(
                    {"_id": doc_id},
                    {"$set": produit_doc}
                )

                # Mettre à jour les caches
                update_all_caches(f"{doc_id}.json")


        return True

    except Exception as e:

        return False















def nettoyer_fichiers_non_references(user_folder, data_json):
    """
    FONCTION DÉPRÉCIÉE - Plus nécessaire avec Cloudinary
    Les médias sont maintenant stockés sur Cloudinary et gérés automatiquement
    """
    # Variables utilisées pour éviter les warnings
    _ = user_folder
    _ = data_json

    # Ne rien faire - les médias sont maintenant sur Cloudinary
    return
    



def extraire_chemin_fichier(url):
    """
    FONCTION DÉPRÉCIÉE - Plus nécessaire avec Cloudinary
    Les URLs sont maintenant des URLs Cloudinary directes
    """
    _ = url  # Éviter le warning de variable non utilisée
    return None


def extraire_chemin_fichier_racine(url, user_folder):
    """
    FONCTION DÉPRÉCIÉE - Plus nécessaire avec Cloudinary
    Les URLs sont maintenant des URLs Cloudinary directes
    """
    _ = url  # Éviter le warning de variable non utilisée
    _ = user_folder  # Éviter le warning de variable non utilisée
    return None




def merge_dicts(base, updates):
    for key, value in updates.items():
        if key == "produits" and isinstance(value, list):
            # Créer un dictionnaire indexé par ID pour les anciens produits
            base_produits = {p[0]: p for p in base.get("produits", []) if len(p) > 0}
            for produit in value:
                if len(produit) > 0:
                    base_produits[produit[0]] = produit  # Remplace si existe, ajoute sinon
            base["produits"] = list(base_produits.values())  # Réécrire la liste fusionnée
        elif isinstance(value, dict) and key in base and isinstance(base[key], dict):
            merge_dicts(base[key], value)
        else:
            base[key] = value










def enregistrer_par_pays(json_data):
    
   
    
    
    
    #DOSSIER = "/home/<USER>/Desktop/FastiDz/Produit/"
    produits = json_data.get("produits", [])
   
    
    uid = json_data.get("id")
    
    
    utilisateur_info =  [json_data.get("utilisateur").get("nom") ,
                         
                        json_data.get("utilisateur").get("image_profil") ,
                        json_data.get("utilisateur").get("image_couverture"),
                        json_data.get("membre") ,
                        json_data.get("utilisateur").get("description") , 
                        
                        
                        ]

    fichiers_existants = {}
    for doc in collection_Produit.find():
        fichiers_existants[f"{doc['_id']}.json"] = {k: v for k, v in doc.items() if k != "_id"}

                
                
                
                

    # Supprimer les anciens emplacements des produits à mettre à jour
    for produit in produits:

        if len(produit) > 6 :  # Vérifier qu'il y a assez d'éléments
            pays = produit[6]
            categorie = produit[1]
            produit_id = produit[0]

            for fichier, contenu in fichiers_existants.items():
                for cat, produits_list in contenu.items():
                    contenu[cat] = [
                        p for p in produits_list
                        if not (p["uuid"] == uid and p["produit"] == produit_id)
                    ]

    # Ajouter les produits dans les bons emplacements
    
    
    
    for produit in produits:


        if len(produit) > 14:  # Vérifier qu'il y a tous les éléments nécessaires
            pays = produit[6]
            categorie = produit[1]
            produit_id = produit[0]

            objet = {
                "uuid": uid,
                "produit": produit_id,
                "image": produit[9] if len(produit) > 9 else "",
                "video" : produit[10] if len(produit) > 10 else "",
                "nome": [produit[2], produit[3]] if len(produit) > 3 else ["", ""],
                "prix": produit[7] if len(produit) > 7 else 0,
                "Promotion": produit[8] if len(produit) > 8 else 0,
                "note": produit[4] if len(produit) > 4 else 0,
                "commentair": produit[5] if len(produit) > 5 else "",
                "view": produit[11] if len(produit) > 11 else 0,
                "utilisateur_info" : utilisateur_info,
                "pub" :  produit[13] if len(produit) > 13 else "",
                "Time" : produit[14].get("Time") if len(produit) > 14 and isinstance(produit[14], dict) else ""
            }
            
            
            ###########################PUB##########################################
            
            # Charger les anciennes données si le fichier existe
            
            FoNCTIONgestionPublisité(uid , produit_id , produit[13] , pays , categorie )
            
            
            ##########################################################################  
            
            

            fichier_pays = f"{pays}.json"
            if fichier_pays not in fichiers_existants:
                fichiers_existants[fichier_pays] = {}

            if categorie not in fichiers_existants[fichier_pays]:
                fichiers_existants[fichier_pays][categorie] = []

            fichiers_existants[fichier_pays][categorie].append(objet)

    # Enregistrer les fichiers mis à jour
    modified_files = []
    for fichier, contenu in fichiers_existants.items():
        try:
            # Extraire l'identifiant depuis le nom du fichier, ex: "1.json" → 1
            id_num = int(fichier.replace(".json", ""))
            
            # Mettre à jour le document dans MongoDB
            collection_Produit.update_one({"_id": id_num}, {"$set": contenu})
            
            modified_files.append(fichier)
        except Exception as e:
            pass

    # Mettre à jour les caches pour tous les utilisateurs et appareils
    for fichier in modified_files:
        update_all_caches(fichier)
        
        


def FoNCTIONgestionPublisité(uid, produit_id, produit_Pub, pays, categorie):
    """
    Version MongoDB optimisée pour la gestion des publicités
    """
    if produit_Pub:  # Vérifie que le champ n'est pas vide
        match = re.search(r'_([0-9]+)\.jpg$', produit_Pub)
        if match:
            Jour = int(match.group(1))
            date_pub = (datetime.now() + timedelta(days=Jour)).strftime('%Y-%m-%d')

            # Créer le document de publication
            pub_doc = {
                "pays": str(pays),
                "categorie": str(categorie),
                "uuid": uid,
                "produit": produit_id,
                "pub": date_pub
            }

            # Utiliser upsert pour mettre à jour ou insérer
            collection_publications.update_one(
                {"uuid": uid, "produit": produit_id},
                {"$set": pub_doc},
                upsert=True
            )






# Fonctions utilitaires dépréciées - remplacées par MongoDB
def charger_json(chemin):
    """DÉPRÉCIÉ - Utiliser MongoDB à la place"""
    _ = chemin  # Éviter le warning de variable non utilisée
    return {}

def sauvegarder_json(chemin, data):
    """DÉPRÉCIÉ - Utiliser MongoDB à la place"""
    _ = chemin  # Éviter le warning de variable non utilisée
    _ = data  # Éviter le warning de variable non utilisée
    pass

def supprimer_produits_par_uuid(uid, dossier_produits):
    """
    DÉPRÉCIÉ - Utiliser supprimer_produits_par_uuid_mongodb() à la place
    """
    _ = dossier_produits  # Éviter le warning de variable non utilisée
    return supprimer_produits_par_uuid_mongodb(uid)

def supprimer_media_cloudinary(url):
    """
    Supprime un média (image ou vidéo) de Cloudinary à partir de son URL
    """
    try:
        # Extraire le public_id depuis l'URL Cloudinary
        # Format URL: https://res.cloudinary.com/dboty16jo/image/upload/v1234567890/uid/productId/filename.jpg
        if "cloudinary.com" in url:
            # Diviser l'URL pour extraire le public_id
            parts = url.split("/")
            # Trouver l'index après "upload"
            upload_index = parts.index("upload")
            # Le public_id commence après "upload" et la version (v1234567890)
            public_id_parts = parts[upload_index + 2:]  # Skip "upload" et version
            public_id = "/".join(public_id_parts)

            # Supprimer l'extension du fichier
            if "." in public_id:
                public_id = public_id.rsplit(".", 1)[0]

            # Déterminer le type de ressource
            resource_type = "video" if any(ext in url.lower() for ext in [".mp4", ".mov", ".avi"]) else "image"

            # Supprimer de Cloudinary
            result = cloudinary.uploader.destroy(public_id, resource_type=resource_type)

            return result.get("result") == "ok"
        else:

            return True

    except Exception as e:

        return False
      
       














############################################################################

@app.route('/check_produits_extraireProduitsEtTime', methods=['POST'])
def check_produits_extraireProduitsEtTime():
    # Utiliser la fonction de décompression automatique
    data = get_request_data()
    user_id = data.get("id")
    produits_client = data.get("produit", [])

    # Récupérer les données utilisateur depuis MongoDB
    user_doc = collection_users.find_one({"id": user_id})
    if not user_doc:
        return jsonify({"error": "Utilisateur non trouvé"}), 404

    produits_server = user_doc.get("produits", [])

    # Création des dictionnaires pour accès rapide
    dict_client = {p[0]: p[1] for p in produits_client}  # {'P1': '26/05/2025/14/28/28'}
    dict_server = {p[0]: p for p in produits_server}     # {'P1': [...], 'P2': [...]}

    ADD = []
    UPDATE = []
    SUPP = []

    # Produit présent dans le serveur mais pas chez le client : ADD
    for pid, prod in dict_server.items():
        if pid not in dict_client:
            ADD.append(prod)
        else:
            # Comparer les Time
            time_server = prod[-1].get("Time") if isinstance(prod[-1], dict) else None
            time_client = dict_client[pid]
            if time_server != time_client:
                UPDATE.append(prod)

    # Produit présent chez le client mais plus sur le serveur : SUPP
    for pid, time_client in dict_client.items():
        if pid not in dict_server:
            SUPP.append([pid, time_client])



    # Utiliser la fonction de compression automatique pour la réponse
    return jsonify_compressed({
        "ADD": ADD,
        "UPDATE": UPDATE,
        "SUPP": SUPP
    })


#############################################################################





@app.route("/envoyerEtMettreAJour_dataOld", methods=["POST"])
def envoyerEtMettreAJour_dataOld():
    # Utiliser la fonction de décompression automatique
    data = get_request_data()

    if not data or "uuid" not in data or "clé" not in data:
        return jsonify({"error": "Requête invalide"}), 400

    uuid = data["uuid"]
    cles_demandées = data["clé"]

    # Récupérer les données utilisateur depuis MongoDB
    user_doc = collection_users.find_one({"id": uuid})
    if not user_doc:
        return jsonify({"error": "Utilisateur introuvable"}), 404

    try:
        # Ne retourner que les clés demandées
        result = {}
        for clé in cles_demandées:
            if clé in user_doc:
                result[clé] = user_doc[clé]

      

        # Utiliser la fonction de compression automatique pour la réponse
        return jsonify_compressed(result), 200

    except Exception as e:
        return jsonify({"error": "Erreur serveur", "details": str(e)}), 500



















@app.route("/DayActuale", methods=["POST"])
def DayActuale():
    now = datetime.now()
    formatted = now.isoformat()
    return jsonify({"Time" : formatted})







collection_users = db["users"]


#####################################################""

@app.route("/users", methods=["POST"])
def upload():
    Time = datetime.now().strftime("%d/%m/%Y/%H/%M/%S")

    # Gérer les données JSON de différentes façons
    json_raw = None
    data = None

    # 1. Essayer d'abord les données compressées dans form
    compressed_json = request.form.get("compressed_json")
    if compressed_json:
        try:
            json_raw = decompresser(compressed_json)

        except Exception as e:
            return jsonify({"error": "Erreur décompression des données"}), 400

    # 2. Essayer les données JSON brutes dans form
    if not json_raw:
        json_raw = request.form.get("json")
        if json_raw:
            pass


    # 3. Essayer les données JSON directes
    if not json_raw:
        try:
            data = request.get_json()
            if data:
                pass
        except Exception as e:
            pass


    # Parser le JSON si on l'a en string
    if json_raw and not data:
        try:
            data = json.loads(json_raw)
        except Exception as e:
            return jsonify({"error": "JSON invalide", "details": str(e)}), 400

    if not data:

        return jsonify({"error": "Aucune donnée JSON reçue"}), 400
        
    # Remplacement du champ "Time" dans chaque produit
    produits = data.get("produits", [])
    for produit in produits:
        if isinstance(produit, list) and len(produit) > 11:
            details = produit[14]
            if isinstance(details, dict) and "Time" in details:
                details["Time"] = Time  # Remplacement par la vraie valeur
    
    json_data = data
    
    uid = json_data.get("id")
    if not uid:
        
        return jsonify({"error": "UID manquant dans le JSON"}), 400
   
    
    # Plus besoin de créer des dossiers locaux - tout est sur Cloudinary
    # user_folder = os.path.join(UPLOAD_FOLDER, uid)
    # os.makedirs(user_folder, exist_ok=True)

    # Récupérer les données utilisateur existantes depuis MongoDB
    existing_user = collection_users.find_one({"id": uid})
    
    
    try:
        # Récupère les images de l'ancien produit correspondant
        old_images = next(
            (
                [p[10]] + p[12]['images']
                for p in existing_user.get('produits', [])
                if (
                    p[0] == data.get('produits', [])[0][0]
                    and len(p) > 12
                    and isinstance(p[12], dict)
                    and 'images' in p[12]
                )
            ),
            []
        )

        # Récupère les images du produit dans les nouvelles données
        new_images_all = [
            [p[10]] + p[12]['images']
            for p in data.get('produits', [])
            if (
                len(p) > 12
                and isinstance(p[12], dict)
                and 'images' in p[12]
                and isinstance(p[10], str)  # vérifie que p[10] est bien une URL
            )
        ]
        new_images = new_images_all[0] if new_images_all else []

        # Supprime les images qui ne sont plus présentes
        for url in set(old_images) - set(new_images):
            supprimer_media_cloudinary(url)

    except Exception as e:
        pass






    # Charger l'ancien JSON s'il existe
    if existing_user:
        old_data = {k: v for k, v in existing_user.items() if k not in ["_id", "id"]}
    else:
        old_data = {}
    
    # Fusionner les champs modifiés
    merge_dicts(old_data, json_data)
    if "membre" not in old_data:
        date_ajd = datetime.now().strftime("%d/%m/%Y")
        old_data["membre"] = date_ajd
        
    
    
    # Sauvegarder le JSON fusionné
    collection_users.update_one(
        {"id": uid},
        {"$set": old_data},
        upsert=True  # Crée le document s’il n'existe pas   
    )   
    
    
    # Plus besoin de nettoyer les fichiers locaux - tout est sur Cloudinary
    # nettoyer_fichiers_non_references(user_folder, old_data)

    # Après la sauvegarde du JSON fusionné :
    enregistrer_par_pays(old_data)

    # Plus besoin de gérer les fichiers image localement - tout est sur Cloudinary
    # Les images de profil et couverture sont maintenant des URLs Cloudinary dans le JSON
        
    response = {
        "membre": old_data["membre"] , "Time" : Time
    }
    

    
    return jsonify(response)





####################################################






@app.route("/users/<uid>/data.json", methods=["GET"])
def get_user_json(uid):
    # Récupérer les données utilisateur depuis MongoDB
    user_doc = collection_users.find_one({"id": uid})
    if not user_doc:
        return jsonify({"error": "Utilisateur non trouvé"}), 404

    # Supprimer les champs MongoDB internes
    if "_id" in user_doc:
        del user_doc["_id"]

  

    # Utiliser la fonction de compression automatique pour la réponse
    return jsonify_compressed(user_doc)


@app.route("/users/<uid>", methods=["DELETE"])
def delete_user(uid):
    try:
        # Supprimer les produits de l'utilisateur dans MongoDB Produit
        # Cette fonction supprime maintenant aussi les médias Cloudinary
        supprimer_produits_par_uuid_mongodb(uid)

        # Supprimer toutes les données utilisateur de MongoDB
        collection_users.delete_one({"id": uid})
        collection_statistics.delete_one({"uid": uid})
        collection_publications.delete_many({"uuid": uid})
        collection_reference.delete_many({"uid": uid})
        collection_cache.delete_many({"uid": uid})

        



        return jsonify({"status": "success", "message": f"Utilisateur {uid} et toutes ses données supprimés"}), 200

    except Exception as e:
        return jsonify({"error": "Erreur suppression utilisateur", "details": str(e)}), 500





@app.route("/Produit/<filename>", methods=["GET"])
def get_product_file(filename):
    uid = request.args.get("uid")
    device_id = request.args.get("device_id")
    is_new_install = request.args.get("new_install") == "true"

    if not uid or not device_id:
        return jsonify({"error": "Paramètres 'uid' et 'device_id' requis."}), 400

    doc_id = int(filename.replace(".json", ""))
    produit_doc = collection_Produit.find_one({"_id": doc_id})

    if not produit_doc:
        return jsonify({"error": f"Produit avec _id={doc_id} introuvable."}), 404

    cache_filter = {"uid": uid, "device_id": device_id, "file_id": doc_id}
    ref_filter = {"uid": uid, "device_id": device_id, "file_id": doc_id}

    if is_new_install:
        collection_reference.delete_many(ref_filter)
        collection_cache.delete_many(cache_filter)

    # Vérifier si une référence existe pour ce device
    reference_doc = collection_reference.find_one(ref_filter)

    if not reference_doc:
        # Première fois - créer la référence et envoyer tout
        collection_reference.insert_one({
            **ref_filter,
            "data": produit_doc
        })

        # Créer un cache vide pour la prochaine fois
        empty_cache = {
            **cache_filter,
            **{cat: {"add": [], "delet": [], "update": []} for cat in produit_doc if cat != "_id"}
        }
        collection_cache.replace_one(cache_filter, empty_cache, upsert=True)

        # Envoyer tous les produits
        full_cache = {
            cat: {
                "add": produit_doc[cat],
                "delet": [],
                "update": []
            } for cat in produit_doc if cat != "_id"
        }


        return jsonify_compressed(full_cache)

    # Calculer les différences depuis la dernière synchronisation
    diff_data = update_user_device_cache(uid, device_id, doc_id)

    if diff_data:
        # Il y a des changements - les envoyer


        # Mettre à jour la référence avec les nouvelles données
        collection_reference.update_one(
            ref_filter,
            {"$set": {"data": produit_doc}}
        )

        # Réinitialiser le cache après envoi
        empty_cache = {
            **cache_filter,
            **{cat: {"add": [], "delet": [], "update": []} for cat in produit_doc if cat != "_id"}
        }
        collection_cache.replace_one(cache_filter, empty_cache, upsert=True)



        return jsonify_compressed(diff_data)
    else:
        # Aucun changement - envoyer une réponse vide

        return jsonify({})







@app.route("/users/<uid>/updateProductVideo", methods=["POST"])
def update_product_video(uid):
    """
    Nouvelle route pour recevoir l'URL de vidéo depuis Cloudinary
    Peut créer un nouveau produit si il n'existe pas et que les données complètes sont fournies
    """
    try:
        data = request.get_json()
        product_id = data.get("produit")
        video_url = data.get("videoUrl")

        if not product_id or not video_url:
            return jsonify({"error": "Missing produit or videoUrl"}), 400

        # Récupérer les données utilisateur depuis MongoDB
        user_doc = collection_users.find_one({"id": uid})
        if not user_doc:
            return jsonify({"error": "Utilisateur non trouvé"}), 404

        # Mettre à jour l'URL de vidéo dans les produits de l'utilisateur
        produits = user_doc.get("produits", [])
        updated = False

        for produit in produits:
            if len(produit) > 10 and produit[0] == product_id:
                # Mettre à jour l'URL de vidéo (index 10)
                produit[10] = video_url
                updated = True
                break

        if updated:
            # Sauvegarder dans MongoDB
            collection_users.update_one(
                {"id": uid},
                {"$set": {"produits": produits}}
            )

            # Mettre à jour les caches et les données par pays
            user_doc_updated = collection_users.find_one({"id": uid})
            enregistrer_par_pays(user_doc_updated)

            return jsonify({"status": "success", "message": "Vidéo mise à jour"})
        else:
            # Produit non trouvé - vérifier si on peut le créer
            product_data = data.get("productData")
            if product_data:
                # Créer un nouveau produit avec les données fournies
                Time = datetime.now().strftime("%d/%m/%Y/%H/%M/%S")

                nouveau_produit = [
                    product_id,  # 0: ID produit
                    product_data.get("categorie", "Divers"),  # 1: Catégorie
                    product_data.get("nom", ""),  # 2: Nom
                    product_data.get("description", ""),  # 3: Description
                    product_data.get("note", 0),  # 4: Note
                    product_data.get("commentaires", 0),  # 5: Nombre commentaires
                    product_data.get("pays", "1"),  # 6: Pays
                    product_data.get("prix", 0),  # 7: Prix
                    product_data.get("promotion", 0),  # 8: Promotion
                    "",  # 9: Image principale (vide par défaut)
                    video_url,  # 10: Vidéo
                    0,  # 11: Vues
                    {"images": []},  # 12: Images supplémentaires (vide par défaut)
                    "",  # 13: Pub
                    {"Time": Time}  # 14: Time
                ]

                # Ajouter le nouveau produit
                produits.append(nouveau_produit)

                # Sauvegarder dans MongoDB
                collection_users.update_one(
                    {"id": uid},
                    {"$set": {"produits": produits}}
                )

                # Mettre à jour les caches et les données par pays
                user_doc_updated = collection_users.find_one({"id": uid})
                enregistrer_par_pays(user_doc_updated)

                return jsonify({"status": "success", "message": "Nouveau produit créé avec vidéo"})
            else:
                return jsonify({
                    "error": "Produit non trouvé",
                    "message": "Pour créer un nouveau produit, fournissez 'productData' avec les informations du produit"
                }), 404

    except Exception as e:
        return jsonify({"error": "Erreur serveur", "details": str(e)}), 500

@app.route('/upload_Video_Produit', methods=['POST'])
def upload_Video_Produit():
    """
    ROUTE DÉPRÉCIÉE - Utiliser updateProductVideo à la place
    Le client utilise maintenant Cloudinary directement
    """
    return jsonify({
        "error": "Route dépréciée",
        "message": "Utiliser /users/{uid}/updateProductVideo avec l'URL Cloudinary"
    }), 410




############################################################################################################

@app.route("/incrementView", methods=["POST"])
def increment_product_view():
    try:
        # Utiliser la fonction de décompression automatique
        data = get_request_data()

        if not data:
            return jsonify({"error": "Aucune donnée JSON reçue"}), 400

        uuid = data.get("uuid")
        product_id = data.get("productId")
        graphe_kotlin = data.get("graphe")
        view_is_true_or_false = int(data.get("view"))
        
        if view_is_true_or_false == 1 :
            # Sinon : view == 1 => faire le travail normalement
            ajouter_valeur_dans_graphe_mongodb(uuid, graphe_kotlin, 1)

        if not uuid or not product_id:
            return jsonify({"error": "UUID ou productId manquant"}), 400

        # Récupérer les données utilisateur depuis MongoDB
        user_doc = collection_users.find_one({"id": uuid})
        if not user_doc:
            return jsonify({"error": "Utilisateur introuvable"}), 404

        user_data = user_doc

        # Limiter les commentaires à 20 éléments
        for produit in user_data.get("produits", []):
            if len(produit) > 2 and isinstance(produit[-2], dict) and "Commentaires" in produit[-2]:
                produit[-2]["Commentaires"] = produit[-2]["Commentaires"][:20]

        # Si view est 0 : on ne fait rien, juste on retourne les produits
        if view_is_true_or_false == 0:
            
           
            
            
            return jsonify_compressed({
                "produits": [
                    x for x in [user_data["produits"][i] for i in range(len(user_data["produits"]))
                                if user_data["produits"][i][0] == product_id and len(user_data["produits"][i]) > 1]
                ] + [
                    x for x in user_data["produits"] if x[0] != product_id and len(x) > 1
                ] + [
                    len(user_data["utilisateur"]["nombre_abonnes"]),
                    user_data["utilisateur"]["nombre_suivite"]
                ]
            })



        # Version MongoDB optimisée pour incrémenter les vues
        modified_files = []
        total_incremented = 0

        # Récupérer tous les documents de produits depuis MongoDB
        produit_docs = collection_Produit.find({})

        for produit_doc in produit_docs:
            doc_id = produit_doc["_id"]
            modified = False

            # Parcourir toutes les catégories
            for categorie in produit_doc:
                if categorie == "_id":
                    continue

                produits_list = produit_doc[categorie]
                if isinstance(produits_list, list):
                    for i, produit in enumerate(produits_list):
                        if produit.get("uuid") == uuid and produit.get("produit") == product_id:
                            current_views = int(produit.get("view", 0))
                            produit_doc[categorie][i]["view"] = current_views + 1
                            modified = True
                            total_incremented += 1

            if modified:
                # Sauvegarder dans MongoDB
                collection_Produit.update_one(
                    {"_id": doc_id},
                    {"$set": produit_doc}
                )
                modified_files.append(f"{doc_id}.json")

        user_data_updated = False

        if "produits" in user_data:
            for i, produit in enumerate(user_data["produits"]):
                if len(produit) > 11 and produit[0] == product_id:
                    current_views = int(produit[11])
                    user_data["produits"][i][11] = current_views + 1
                    user_data_updated = True

        if user_data_updated:
            # Sauvegarder dans MongoDB au lieu du fichier JSON
            collection_users.update_one(
                {"id": uuid},
                {"$set": {"produits": user_data["produits"]}}
            )

        for fichier in modified_files:
            update_all_caches(fichier)

        
       
        return jsonify_compressed({
            "produits": [
                x for x in [user_data["produits"][i] for i in range(len(user_data["produits"]))
                            if user_data["produits"][i][0] == product_id and len(user_data["produits"][i]) > 1]
            ] + [
                x for x in user_data["produits"] if x[0] != product_id and len(x) > 1
            ] + [
                len(user_data["utilisateur"]["nombre_abonnes"]),
                user_data["utilisateur"]["nombre_suivite"]
            ]
        })

    except Exception as e:

        return jsonify({"error": str(e)}), 500

###########################################################################################################

@app.route('/envoyerDonnees_commaintair_avec_index', methods=['POST'])
def envoyerDonnees_commaintair_avec_index():
    data = request.get_json()

    uuid = data.get("uuid")
    produitid = data.get("produitid")
    index = data.get("index")

    if not uuid or not produitid or index is None:
        return jsonify({"error": "Paramètres manquants"}), 400

    try:
        # Récupérer les données utilisateur depuis MongoDB
        user_doc = collection_users.find_one({"id": uuid})
        if not user_doc:
            return jsonify({"error": "Utilisateur non trouvé"}), 404

        produits = user_doc.get("produits", [])

        # Trouver le produit correspondant
        for produit in produits:
            if len(produit) > 0 and produit[0] == produitid:
                # Vérifier que le produit a la structure attendue
                if len(produit) > 12 and isinstance(produit[12], dict) and "Commentaires" in produit[12]:
                    commentaires = produit[12]["Commentaires"]
                    commentaires_suivants = commentaires[index:index + 5]  # Récupérer 5 commentaires à partir de l'index

                    return jsonify({"commentaires": commentaires_suivants}), 200

        return jsonify({"error": "Produit non trouvé"}), 404

    except Exception as e:
        return jsonify({"error": str(e)}), 500








#update_all_users_all_files
@app.route('/update_user_data_portfolio', methods=['POST'])
def update_user_data():
    data = request.get_json()
    id = data.get("id")
    main_key = data.get("main_key")
    sub_key = data.get("sub_key")
    value = data.get("value")  

    if not all([main_key, sub_key]) or value is None:
        return jsonify({"data": "Paramètres manquants"}), 400

    # Récupérer les données utilisateur depuis MongoDB
    user_doc = collection_users.find_one({"id": id})
    if not user_doc:
        return jsonify({"data": "Utilisateur non trouvé"}), 404
        
        
        
    else:
        content = user_doc

    # S'assurer que la clÃƒÂ© principale existe
    if main_key not in content:
        content[main_key] = {}

    # Cas spÃƒÂ©cial : transactions (dÃƒÂ©pÃƒÂ´ts/retraits)
    if main_key == "transactions":
        if sub_key not in content[main_key]:
            content[main_key][sub_key] = []

        if not isinstance(content[main_key][sub_key], list):
            return jsonify({"error": f"{main_key}.{sub_key} n'est pas une liste"}), 400

        # Ajouter un objet avec montant et date
        content[main_key][sub_key].append({
            "montant": value,
            "date": datetime.now().strftime("%Y-%m-%d")
        })
        
    # Cas spÃ©cial pour les listes simples (ex: List<String>)
    elif isinstance(content[main_key].get(sub_key), list):
        if sub_key not in content[main_key]:
            content[main_key][sub_key] = []

        if not isinstance(value, str):
            return jsonify({"error": "Les listes simples ne peuvent contenir que des chaÃ®nes"}), 400

        string_list = content[main_key][sub_key]

        # VÃ©rifie que tous les Ã©lÃ©ments sont des chaÃ®nes
        if all(isinstance(item, str) for item in string_list):
            if value in string_list:
                string_list.remove(value)
            else:
                string_list.append(value)
        else:
            return jsonify({"error": "La liste contient des Ã©lÃ©ments non textuels"}), 400
        
        
       

        

    else:
        # Sinon, comportement classique (valeurs numÃƒÂ©riques)
        if sub_key not in content[main_key]:
            content[main_key][sub_key] = 0

        if isinstance(value, float) or isinstance(value, int):
            content[main_key][sub_key] = round(content[main_key][sub_key] + value, 2)
        else:
            content[main_key][sub_key] += value

    # Sauvegarder dans MongoDB
    collection_users.update_one(
        {"id": id},
        {"$set": content},
        upsert=True
    )

    return jsonify({"data": datetime.now().strftime("%Y-%m-%d")}), 200






@app.route('/recuperer_jours_suivants', methods=['POST'])
def recuperer_jours_suivants():
    # Utiliser la fonction de décompression automatique
    data = get_request_data()
    user_id = data["id"]
    graphes_indices = data["graphes"]

    # Récupérer les statistiques utilisateur
    stats_doc = collection_statistics.find_one({"uid": user_id})
    all_graphes = stats_doc.get("statistiques", {}).get("graphes", {}) if stats_doc else {}

    # Ajouter "prix" depuis collection_pricing si demandé
    if "prix" in graphes_indices:
        pricing_doc = collection_pricing.find_one({"type": "global"})
        if pricing_doc:
            prix_graphes = pricing_doc.get("statistiques", {}).get("graphes", {})
            all_graphes.update(prix_graphes)

    result = {}

    for graphe, indices in graphes_indices.items():
        if isinstance(indices, str):
            indices = json.loads(indices)

        if len(indices) == 2:
            mois_idx, jour_idx = indices
            graphe_data = all_graphes.get(graphe, [])
            jours_suivants = []

            for m in range(mois_idx, len(graphe_data)):
                mois_data = graphe_data[m]
                if not mois_data:
                    continue

                jours = mois_data[jour_idx:] if m == mois_idx else mois_data

                if jours:
                    jours_suivants.append([m, jours])  # Inclure index du mois

            if jours_suivants:
                jours_suivants[-1][-1].pop()  # Retirer le dernier jour
                result[graphe] = jours_suivants
            else:
                result[graphe] = []
        else:
            result[graphe] = []


    # Utiliser la fonction de compression automatique pour la réponse
    return jsonify_compressed(result)




"""

@app.route('/recuperer_jours_suivants', methods=['POST'])
def recuperer_jours_suivants():
    data = request.get_json()
    user_id = data["id"]
    graphes_indices = data["graphes"]

    # Récupérer les statistiques utilisateur depuis MongoDB
    stats_doc = collection_statistics.find_one({"uid": user_id})
    if not stats_doc:
        prix_liste = collection_pricing.find_one({"type": "global"}).get("statistiques", {}).get("graphes", {}).get("prix", [])
        prix_data = [[i, mois] for i, mois in enumerate(prix_liste)]

        return jsonify({"prix": prix_data}), 200

    all_graphes = stats_doc.get("statistiques", {}).get("graphes", {})

    # Charger les données de prix depuis MongoDB si "prix" est demandé
    if "prix" in graphes_indices:
        pricing_doc = collection_pricing.find_one({"type": "global"})
        if pricing_doc:
            prix_graphes = pricing_doc.get("statistiques", {}).get("graphes", {})
            all_graphes.update(prix_graphes)

    result = {}

    for graphe, indices in graphes_indices.items():
        if isinstance(indices, str):
            indices = json.loads(indices)

        if len(indices) == 2:
            mois_idx, jour_idx = indices
            graphe_data = all_graphes.get(graphe, [])
            jours_suivants = []

            for m in range(mois_idx, len(graphe_data)):
                mois_data = graphe_data[m]
                if not mois_data:
                    continue

                if m == mois_idx:
                    jours = mois_data[jour_idx:]
                else:
                    jours = mois_data

                if jours:
                    jours_suivants.append([m, jours])  # inclure index du mois

            if jours_suivants:
                jours_suivants[-1][-1].pop()
                result[graphe] = jours_suivants
            else:
                result[graphe] = []
        else:
            result[graphe] = []
            
         

    return jsonify(result)
"""








@app.route('/Commentaire_User', methods=['POST'])
def Commentaire_User():
    try:
        # Récupérer le JSON envoyé par le client
        data = request.get_json()
    
        id_my = data.get("id")
        uuid = data.get("uuid")
        product_id = data.get("product_id")
        action = data.get("action")
        Time_Actuale = datetime.now().strftime("%d/%m/%Y %H:%M")
        
        # Préparer les données du commentaire
        comment = [
            data.get("name"),
            data.get("image"),
            data.get("commentaire"),
            data.get("note"),
            data.get("index"),
            id_my,
            data.get("love"),
            Time_Actuale 
        ]
        
        comment_my = [
            uuid,
            product_id,
            data.get("name"),
            data.get("image"),
            data.get("commentaire"),
            data.get("note"),
            data.get("index"),
            id_my,
            data.get("love"),
            Time_Actuale 
        ]
        
        # Récupérer les données depuis MongoDB
        user_doc = collection_users.find_one({"id": uuid})
        user_doc_my = collection_users.find_one({"id": id_my})

        if not user_doc:
            return jsonify({"status": "error", "message": "Utilisateur propriétaire non trouvé"}), 404
        if not user_doc_my:
            return jsonify({"status": "error", "message": "Utilisateur commentateur non trouvé"}), 404

        produits = user_doc.get("produits", [])
        my_Commaintair = user_doc_my.get("Commentaires", [])

        # Fonction pour trouver l'index du commentaire dans la liste
        def find_comment_index(comments_list, id_my, index_value):
            for i, comment in enumerate(comments_list):
                # Vérifier si le commentaire a le bon ID et index
                if comment[5] == id_my and comment[4] == index_value:
                    return i
            return -1
        
        # Fonction pour trouver l'index du commentaire dans my_Commaintair
        def find_my_comment_index(comments_list, uuid_value, product_id_value, index_value):
            for i, comment in enumerate(comments_list):
                # Vérifier si le commentaire a le bon UUID, product_id et index
                if comment[0] == uuid_value and comment[1] == product_id_value and comment[6] == index_value:
                    return i
            return -1

        # Traiter selon l'action
        modified = False
        message = ""
        
        # Rechercher le produit
        for produit in produits:
            if len(produit) > 0 and produit[0] == product_id:
                details = produit[12].get("Commentaires", [])
                
                
                
                
                
                
                
                
                
                    
                

                
                
                
                
                if action == "ADD":
                    details.append(comment)
                    my_Commaintair.append(comment_my)
                    modified = True
                    message = "Commentaire ajouté"
                    
                elif action == "SUP":
                    index_value = data.get("index")
                    
                    # Supprimer le commentaire du produit
                    comment_index = find_comment_index(details, id_my, index_value)
                    if comment_index != -1:
                        details.pop(comment_index)
                        modified = True
                        
                    # Supprimer le commentaire de my_Commaintair
                    my_comment_index = find_my_comment_index(my_Commaintair, uuid, product_id, index_value)
                    if my_comment_index != -1:
                        my_Commaintair.pop(my_comment_index)
                        modified = True
                        
                    message = "Commentaire supprimé" if modified else "Commentaire non trouvé"
                    
                elif action == "UPDAT":
                    index_value = data.get("index")
                    
                    # Mettre à jour le commentaire du produit
                    comment_index = find_comment_index(details, id_my, index_value)
                    if comment_index != -1:
                        details[comment_index] = comment
                        modified = True
                        
                    # Mettre à jour le commentaire dans my_Commaintair
                    my_comment_index = find_my_comment_index(my_Commaintair, uuid, product_id, index_value)
                    if my_comment_index != -1:
                        my_Commaintair[my_comment_index] = comment_my
                        modified = True
                        
                    message = "Commentaire mis à jour" if modified else "Commentaire non trouvé"
                    
                
                
                
                
                
                liste_note_moiyane = []
                
                for note_moiyan in details :
                    liste_note_moiyane.append(note_moiyan[3])
                    
                    
                    
               
                
                
               
                
                # Version MongoDB optimisée pour mettre à jour les notes et commentaires
                pays_id = int(produit[6])
                categorie = produit[1]

                # Récupérer le document produit depuis MongoDB
                produit_doc = collection_Produit.find_one({"_id": pays_id})
                if produit_doc and categorie in produit_doc:
                    TypeofProduit = produit_doc[categorie]

                    for i, TypeofProduit_ in enumerate(TypeofProduit):
                        if TypeofProduit_.get("produit") == product_id and TypeofProduit_.get("uuid") == uuid:
                            # Calculer la nouvelle note moyenne
                            if liste_note_moiyane:
                                nouvelle_note = sum(liste_note_moiyane) / len(liste_note_moiyane)
                                TypeofProduit_["note"] = nouvelle_note
                                TypeofProduit_["commentair"] = len(details)

                                # Mettre à jour aussi dans les données utilisateur
                                produit[4] = nouvelle_note
                                produit[5] = len(details)

                                # Sauvegarder dans MongoDB
                                collection_Produit.update_one(
                                    {"_id": pays_id},
                                    {"$set": {f"{categorie}.{i}": TypeofProduit_}}
                                )

                                update_all_caches(f"{pays_id}.json")
                                break
                
                
                
                
                
                
                
                break
        else:
            return jsonify({"status": "error", "message": "Produit non trouvé"}), 404

        if not modified:
            return jsonify({"status": "warning", "message": message}), 200

        # Sauvegarder les modifications dans MongoDB
        collection_users.update_one(
            {"id": uuid},
            {"$set": {"produits": produits}}
        )

        collection_users.update_one(
            {"id": id_my},
            {"$set": {"Commentaires": my_Commaintair}}
        )
            
            
        

        return jsonify({"time": Time_Actuale}), 200

    except Exception as e:
     
        return jsonify({"status": "error", "message": str(e)}), 500








@app.route('/Update_User_Profile', methods=['POST'])
def Update_User_Profile():
    try:
        # Récupérer le JSON envoyé par le client
        data = request.get_json()
        id_my = data.get("id")  # ID de l'utilisateur qui a changé son profil
        new_name = data.get("name")  # Nouveau nom
        new_image = data.get("image")  # Nouvelle image
        
        # Vérifier si l'ID est fourni et si au moins un paramètre non vide est fourni
        if not id_my:
            return jsonify({"status": "error", "message": "ID utilisateur manquant"}), 400
            
        # Vérifier si au moins un paramètre valide est fourni (non vide)
        if (new_name is None or new_name == "") and (new_image is None or new_image == ""):
            return jsonify({"status": "error", "message": "Au moins un paramètre valide (nom ou image) est requis"}), 400
            
        # Récupérer les données utilisateur depuis MongoDB
        user_doc = collection_users.find_one({"id": id_my})
        if not user_doc:
            return jsonify({"status": "error", "message": "Utilisateur non trouvé"}), 404

        # Récupérer les commentaires de l'utilisateur
        my_comments = user_doc.get("Commentaires", [])
        
        # Compteurs pour les statistiques
        updated_comments = 0
        updated_files = set()
        
        # Parcourir tous les commentaires de l'utilisateur
        for comment in my_comments:
            if len(comment) >= 10:  # Vérifier que le commentaire a le bon format
                uuid_target = comment[0]  # UUID du propriétaire du produit
                product_id = comment[1]  # ID du produit
                index_value = comment[6]  # Index du commentaire
                
                # Mettre à jour le commentaire dans la liste de l'utilisateur, seulement si valeur non vide
                if new_name is not None and new_name != "":
                    comment[2] = new_name
                if new_image is not None and new_image != "":
                    comment[3] = new_image
                
                try:
                    # Récupérer les données du propriétaire du produit depuis MongoDB
                    target_doc = collection_users.find_one({"id": uuid_target})
                    if not target_doc:
                        continue
                    
                    # Parcourir les produits pour trouver celui qui contient le commentaire
                    produits = target_doc.get("produits", [])
                    target_modified = False

                    for produit in produits:
                        if len(produit) > 0 and produit[0] == product_id:
                            if len(produit) > 12 and isinstance(produit[12], dict):
                                commentaires = produit[12].get("Commentaires", [])

                                # Fonction pour trouver l'index du commentaire dans la liste
                                def find_comment_index(comments_list, id_my, index_value):
                                    for i, comm in enumerate(comments_list):
                                        # Vérifier si le commentaire a le bon ID et index
                                        if len(comm) > 5 and comm[5] == id_my and comm[4] == index_value:
                                            return i
                                    return -1

                                # Trouver et mettre à jour le commentaire
                                comment_index = find_comment_index(commentaires, id_my, index_value)
                                if comment_index != -1:
                                    # Mise à jour seulement si valeur non vide
                                    if new_name is not None and new_name != "":
                                        commentaires[comment_index][0] = new_name
                                    if new_image is not None and new_image != "":
                                        commentaires[comment_index][1] = new_image
                                    updated_comments += 1
                                    updated_files.add(uuid_target)
                                    target_modified = True

                    # Sauvegarder les modifications dans MongoDB
                    if target_modified:
                        collection_users.update_one(
                            {"id": uuid_target},
                            {"$set": {"produits": produits}}
                        )
                        
                except Exception as e:
                
                    continue
        
        # Sauvegarder les modifications des commentaires de l'utilisateur dans MongoDB
        collection_users.update_one(
            {"id": id_my},
            {"$set": {"Commentaires": my_comments}}
        )

        message = f"Profil mis à jour: {updated_comments} commentaires modifiés dans {len(updated_files)} utilisateurs"
        

        
        return jsonify({
            "status": "success", 
            "message": message,
            "updated_comments": updated_comments,
            "updated_files": len(updated_files)
        }), 200

    except Exception as e:
        error_message = f"Erreur lors de la mise à jour du profil: {str(e)}"
        return jsonify({"status": "error", "message": error_message}), 500





@app.route('/envoyerDomande_pour_acheter', methods=['POST'])
def envoyerDomande_pour_acheter():
    try:
        data = request.get_json()
        Time_Actuale = datetime.now().strftime("%d/%m/%Y %H:%M")
        
        liste = data.get("liste", {})
        
        # ⬇️ Mise à jour de la date ici
        liste["date"] = Time_Actuale
        
        
        
        
        

        uuid = liste.get("uuid")
        uuid_My = liste.get("uuid_My")
        
        

        if not uuid or not uuid_My:
            return jsonify({"status": "error", "message": "uuid ou uuid_My manquant"}), 400

        # Version MongoDB optimisée pour les demandes d'amitié

        # === Traitement pour uuid (destinataire) ===
        user_doc = collection_users.find_one({"id": uuid})
        if not user_doc:
            user_doc = {"id": uuid, "Demonde": []}

        if "Demonde" not in user_doc:
            user_doc["Demonde"] = []
        user_doc["Demonde"].append(liste)

        collection_users.update_one(
            {"id": uuid},
            {"$set": {"Demonde": user_doc["Demonde"]}},
            upsert=True
        )

        # === Traitement pour uuid_My (expéditeur) ===
        user_doc_my = collection_users.find_one({"id": uuid_My})
        if not user_doc_my:
            user_doc_my = {"id": uuid_My, "MyDemonde": []}

        if "MyDemonde" not in user_doc_my:
            user_doc_my["MyDemonde"] = []
        user_doc_my["MyDemonde"].append(liste)

        collection_users.update_one(
            {"id": uuid_My},
            {"$set": {"MyDemonde": user_doc_my["MyDemonde"]}},
            upsert=True
        )

        return jsonify({"time": Time_Actuale}), 200

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500






@app.route('/synchroniser_demandes', methods=['POST'])
def synchroniser_demandes():
    try:
        data = request.get_json()
        uuid = data.get("uuid")
        identifiants_connus = set(data.get("identifiants", []))

        # Version MongoDB optimisée pour synchroniser les demandes
        user_doc = collection_users.find_one({"id": uuid})
        if not user_doc:
            return jsonify({"Demonde": [], "MyDemonde": []}), 200

        # Si la liste est vide => le client n’a rien => on envoie tout
        if not identifiants_connus:
            return jsonify({
                "Demonde": user_doc.get("Demonde", []),
                "MyDemonde": user_doc.get("MyDemonde", [])
            }), 200

        # Sinon on filtre
        nouveaux_demonde = []
        nouveaux_mydemonde = []

        for obj in user_doc.get("Demonde", []):
            ident = obj.get("idontifion")
            if ident and ident not in identifiants_connus:
                nouveaux_demonde.append(obj)

        for obj in user_doc.get("MyDemonde", []):
            ident = obj.get("idontifion")
            if ident and ident not in identifiants_connus:
                nouveaux_mydemonde.append(obj)

        

        
        return jsonify({
            "Demonde": nouveaux_demonde,
            "MyDemonde": nouveaux_mydemonde
        }), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500








@app.route("/envoyerJson_supremetProduitdansServer", methods=["POST"])
def envoyerJson_supremetProduitdansServer():
    data = request.get_json()
    uuid = data.get("uuid")
    produit = data.get("produit")
   
    
    # Version MongoDB optimisée pour supprimer un produit

    # 1. Supprimer le produit des données utilisateur dans MongoDB
    user_doc = collection_users.find_one({"id": uuid})
    if user_doc and "produits" in user_doc:
        produits = user_doc["produits"]
        categorie = None
        pays = None
        
        #supprimer_media_cloudinary

        # Trouver et supprimer le produit
        for i, sous_liste in enumerate(produits):
            if isinstance(sous_liste, list) and len(sous_liste) > 6 and sous_liste[0] == produit:
                categorie = sous_liste[1]
                pays = sous_liste[6]
                
                for url in produits[i][12]["images"]:
                    supprimer_media_cloudinary(url)
                    
                if produits[i][10]:
                    supprimer_media_cloudinary(produits[i][10])
                
                del produits[i]
                break
            
        

        # Sauvegarder dans MongoDB
        collection_users.update_one(
            {"id": uuid},
            {"$set": {"produits": produits}}
        )

    # 2. Supprimer l'entrée correspondante dans MongoDB Produit
    if categorie and pays:
        pays_id = int(pays)
        produit_doc = collection_Produit.find_one({"_id": pays_id})

        if produit_doc and categorie in produit_doc:
            produit_doc[categorie] = [
                item for item in produit_doc[categorie]
                if not (item.get("uuid") == uuid and item.get("produit") == produit)
            ]

            # Sauvegarder dans MongoDB
            collection_Produit.update_one(
                {"_id": pays_id},
                {"$set": {categorie: produit_doc[categorie]}}
            )

            update_all_caches(f"{pays}.json")

    # 3. Supprimer l'entrée correspondante dans MongoDB publications
    collection_publications.delete_many({
        "uuid": uuid,
        "produit": produit
    })
    
    
        

    return jsonify({"status": "ok"})










def inserer_produits_mongodb():
    structure = {
        "Vêtements": [],
        "Hommes": [],
        "Femmes": [],
        "Enfants": [],
        "Chaussures": [],
        "Accessoires": [],
        "Sacs": [],
        "Montres": [],
        "Lunettes": [],
        "Bijoux": [],
        "Électronique": [],
        "Smartphones": [],
        "Ordinateurs": [],
        "Audio": [],
        "Télévisions": [],
        "Appareils": [],
        "Maison": [],
        "Meubles": [],
        "Luminaires": [],
        "Cuisine": [],
        "Searche": [],
    }

    # Vérifie si la collection est vide
    if collection_Produit.count_documents({}) == 0:

        for i in range(1, 100):
            document = {
                "_id": i,
                **structure
            }
            collection_Produit.insert_one(document)

        return

    # Vérifie si tous les documents de 1 à 9 existent et contiennent toutes les clés
    all_ok = True
    for i in range(1, 100):
        doc = collection_Produit.find_one({"_id": i})
        if not doc:
            all_ok = False
            break
        for key in structure.keys():
            if key not in doc:
                all_ok = False
                break
        if not all_ok:
            break

    if all_ok:
        pass
    else:

        for i in range(1, 100):
            collection_Produit.update_one(
                {"_id": i},
                {"$setOnInsert": {"_id": i, **structure}},
                upsert=True
            )


# Lancer la fonction
inserer_produits_mongodb()











# Lancer le scheduler
start_scheduler()



if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000)
