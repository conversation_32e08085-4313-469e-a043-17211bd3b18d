#!/usr/bin/env python3
"""
Script de validation des corrections apportées au fichier app.py
"""

import ast
import sys
import os

def validate_python_syntax(file_path):
    """Valide la syntaxe Python d'un fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Tenter de parser le fichier
        ast.parse(content)
        return True, "Syntaxe Python valide"
    
    except SyntaxError as e:
        return False, f"Erreur de syntaxe ligne {e.lineno}: {e.msg}"
    except Exception as e:
        return False, f"Erreur lors de la validation: {e}"

def check_imports(file_path):
    """Vérifie les imports du fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    imports.append(f"{module}.{alias.name}")
        
        return True, imports
    
    except Exception as e:
        return False, f"Erreur lors de l'analyse des imports: {e}"

def check_function_definitions(file_path):
    """Vérifie les définitions de fonctions"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                functions.append(node.name)
        
        return True, functions
    
    except Exception as e:
        return False, f"Erreur lors de l'analyse des fonctions: {e}"

def check_optimized_functions(file_path):
    """Vérifie que les fonctions optimisées sont présentes"""
    optimized_functions = [
        "update_all_caches",
        "calculate_cache_diff", 
        "ajouter_valeurs_multiples_dans_graphe_mongodb",
        "calculer_ratio_fallback",
        "nettoyer_pub_expiree_fallback",
        "update_all_caches_fallback"
    ]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_functions = []
        for func in optimized_functions:
            if f"def {func}(" not in content:
                missing_functions.append(func)
        
        return len(missing_functions) == 0, missing_functions
    
    except Exception as e:
        return False, f"Erreur lors de la vérification: {e}"

def check_mongodb_optimizations(file_path):
    """Vérifie que les optimisations MongoDB sont présentes"""
    optimizations = [
        "maxPoolSize=50",  # Configuration du pool
        "bulk_write",      # Opérations en lot
        "find_one_and_update",  # Opérations atomiques
        "$setOnInsert",    # Upsert optimisé
        "aggregate("       # Agrégations
    ]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_optimizations = []
        for opt in optimizations:
            if opt not in content:
                missing_optimizations.append(opt)
        
        return len(missing_optimizations) == 0, missing_optimizations
    
    except Exception as e:
        return False, f"Erreur lors de la vérification: {e}"

def main():
    """Fonction principale de validation"""
    print("🔍 VALIDATION DES CORRECTIONS APPORTÉES À APP.PY")
    print("=" * 60)
    
    file_path = "app.py"
    
    if not os.path.exists(file_path):
        print(f"❌ Fichier {file_path} non trouvé")
        return False
    
    all_checks_passed = True
    
    # 1. Validation de la syntaxe Python
    print("1️⃣ Validation de la syntaxe Python...")
    syntax_valid, syntax_msg = validate_python_syntax(file_path)
    if syntax_valid:
        print(f"   ✅ {syntax_msg}")
    else:
        print(f"   ❌ {syntax_msg}")
        all_checks_passed = False
    
    # 2. Vérification des imports
    print("\n2️⃣ Vérification des imports...")
    imports_valid, imports_result = check_imports(file_path)
    if imports_valid:
        print(f"   ✅ {len(imports_result)} imports trouvés")
        critical_imports = ["flask", "pymongo", "cloudinary"]
        for imp in critical_imports:
            found = any(imp in i for i in imports_result)
            if found:
                print(f"   ✅ Import critique trouvé: {imp}")
            else:
                print(f"   ⚠️ Import critique manquant: {imp}")
    else:
        print(f"   ❌ {imports_result}")
        all_checks_passed = False
    
    # 3. Vérification des fonctions
    print("\n3️⃣ Vérification des définitions de fonctions...")
    functions_valid, functions_result = check_function_definitions(file_path)
    if functions_valid:
        print(f"   ✅ {len(functions_result)} fonctions trouvées")
    else:
        print(f"   ❌ {functions_result}")
        all_checks_passed = False
    
    # 4. Vérification des fonctions optimisées
    print("\n4️⃣ Vérification des fonctions optimisées...")
    optimized_valid, optimized_result = check_optimized_functions(file_path)
    if optimized_valid:
        print("   ✅ Toutes les fonctions optimisées sont présentes")
    else:
        print(f"   ❌ Fonctions optimisées manquantes: {optimized_result}")
        all_checks_passed = False
    
    # 5. Vérification des optimisations MongoDB
    print("\n5️⃣ Vérification des optimisations MongoDB...")
    mongodb_valid, mongodb_result = check_mongodb_optimizations(file_path)
    if mongodb_valid:
        print("   ✅ Toutes les optimisations MongoDB sont présentes")
    else:
        print(f"   ❌ Optimisations MongoDB manquantes: {mongodb_result}")
        all_checks_passed = False
    
    # 6. Vérification de la taille du fichier
    print("\n6️⃣ Vérification de la taille du fichier...")
    file_size = os.path.getsize(file_path)
    line_count = 0
    with open(file_path, 'r', encoding='utf-8') as f:
        line_count = sum(1 for _ in f)
    
    print(f"   📊 Taille du fichier: {file_size:,} bytes")
    print(f"   📊 Nombre de lignes: {line_count:,}")
    
    if line_count > 2000:
        print("   ✅ Fichier de taille appropriée pour une application complexe")
    else:
        print("   ⚠️ Fichier plus petit que prévu")
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DE LA VALIDATION")
    print("=" * 60)
    
    if all_checks_passed:
        print("🎉 TOUTES LES VALIDATIONS SONT PASSÉES!")
        print("\n✅ Le fichier app.py est prêt pour le déploiement")
        print("\n📋 Prochaines étapes recommandées:")
        print("1. Installer les dépendances: python install_dependencies.py")
        print("2. Créer les index MongoDB: python create_mongodb_indexes.py")
        print("3. Tester les performances: python performance_test.py")
        print("4. Déployer les optimisations: python deploy_optimizations.py")
        return True
    else:
        print("❌ CERTAINES VALIDATIONS ONT ÉCHOUÉ")
        print("\n⚠️ Veuillez corriger les erreurs avant de continuer")
        print("\n💡 Conseils:")
        print("- Vérifiez la syntaxe Python")
        print("- Assurez-vous que tous les imports sont corrects")
        print("- Vérifiez que toutes les fonctions optimisées sont présentes")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
    else:
        print("\n🚀 Validation terminée avec succès!")
